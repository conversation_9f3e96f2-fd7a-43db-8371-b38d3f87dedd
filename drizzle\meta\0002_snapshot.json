{"version": "6", "dialect": "sqlite", "id": "9f9802ad-fc03-4e1a-847e-8a73866a9f52", "prevId": "b6fb222a-2134-41a3-a4ef-67aef0062f93", "tables": {"account": {"name": "account", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"account_provider_providerAccountId_pk": {"columns": ["provider", "providerAccountId"], "name": "account_provider_providerAccountId_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "email": {"name": "email", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"email_address_unique": {"name": "email_address_unique", "columns": ["address"], "isUnique": true}}, "foreignKeys": {"email_userId_user_id_fk": {"name": "email_userId_user_id_fk", "tableFrom": "email", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "message": {"name": "message", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "emailId": {"name": "emailId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "from_address": {"name": "from_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "html": {"name": "html", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "received_at": {"name": "received_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"message_emailId_email_id_fk": {"name": "message_emailId_email_id_fk", "tableFrom": "message", "tableTo": "email", "columnsFrom": ["emailId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "session": {"name": "session", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}